import { Module } from '@nestjs/common';
import { UserDeletionService } from './user-deletion.service';
import { UserDeletionController } from './user-deletion.controller';
import { EmailModule } from '@/mail/email.module';

@Module({
  imports: [EmailModule],
  controllers: [UserDeletionController],
  providers: [UserDeletionService],
  exports: [UserDeletionService],
})
export class UserDeletionModule {}
