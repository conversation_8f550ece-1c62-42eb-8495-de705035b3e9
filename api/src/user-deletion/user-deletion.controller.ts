import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { UserDeletionService } from './user-deletion.service';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { User } from '@/guards/user.decorator';
import { type User as IUser } from '@/db/schema';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import {
  DeleteUserWithReassignmentDto,
  ConstraintStatusDto,
  ConstraintFixesResponseDto,
} from './user-deletion.dto';

@ApiTags('User Deletion')
@Controller('user-deletion')
export class UserDeletionController {
  private readonly logger = new Logger(UserDeletionController.name);

  constructor(private readonly userDeletionService: UserDeletionService) {}

  @Get('constraint-status')
  @ApiOperation({
    summary: 'Check constraint status',
    description:
      'Check the current foreign key constraint status for questions.created_by',
  })
  @ApiResponse({
    status: 200,
    description: 'Constraint status retrieved successfully',
    type: ConstraintStatusDto,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async checkConstraintStatus(
    @User() currentUser: IUser,
  ): Promise<ConstraintStatusDto> {
    try {
      this.logger.log(`Checking constraint status by ${currentUser.email}`);
      const status = await this.userDeletionService.checkConstraintStatus();

      this.logger.log(`Constraint status: ${JSON.stringify(status)}`);
      return status;
    } catch (error: any) {
      this.logger.error(`Failed to check constraint status:`, error.message);
      throw error;
    }
  }

  @Post('apply-constraint-fixes')
  @ApiOperation({
    summary: 'Apply constraint fixes',
    description: 'Apply foreign key constraint fixes for user deletion',
  })
  @ApiResponse({
    status: 200,
    description: 'Constraint fixes applied successfully',
    type: ConstraintFixesResponseDto,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async applyConstraintFixes(
    @User() currentUser: IUser,
  ): Promise<ConstraintFixesResponseDto> {
    try {
      this.logger.log(`Applying constraint fixes by ${currentUser.email}`);
      const result = await this.userDeletionService.applyConstraintFixes();

      this.logger.log(`Constraint fixes result: ${JSON.stringify(result)}`);
      return {
        success: result.success,
        message: result.message,
        appliedFixes: [...result.appliedFixes], // Convert readonly array to mutable
      };
    } catch (error: any) {
      this.logger.error(`Failed to apply constraint fixes:`, error.message);
      throw error;
    }
  }

  @Delete(':userId/with-reassignment')
  @ApiOperation({
    summary: 'Delete user with question reassignment',
    description:
      'Delete a user with optional question reassignment to another user',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'User deleted successfully',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async deleteUserWithReassignment(
    @Param('userId', CustomParseUUIDPipe) userId: string,
    @Body() body: DeleteUserWithReassignmentDto,
    @User() currentUser: IUser,
  ): Promise<any> {
    try {
      this.logger.log(
        `Deleting user ${userId} with reassignment by ${currentUser.email}`,
      );

      const result =
        await this.userDeletionService.deleteUserWithQuestionReassignment(
          userId,
          body.replacementUserEmail,
          body.sendNotification,
          currentUser.id,
        );

      this.logger.log(`User deletion completed: ${JSON.stringify(result)}`);
      return result;
    } catch (error: any) {
      this.logger.error(`Failed to delete user ${userId}:`, error.message);
      throw error;
    }
  }
}
