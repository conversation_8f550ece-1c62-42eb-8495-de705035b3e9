import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { NotificationService } from '../notification/notification.service';
import { EmailService } from '@/mail/email.service';
import { Subject } from 'rxjs';
import { DeviceTokenService } from './device-token.service';
import { UserRepository } from '@/repositories/user.repository';
import { QueueService } from '../queue/queue.service';
import { users } from '@/db/schema/users';
import { eq } from 'drizzle-orm';

@Injectable()
export class NotificationChannelService {
  private readonly logger = new Logger(NotificationChannelService.name);
  private readonly inAppNotificationSubjects: Map<string, Subject<any>> =
    new Map();

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly emailService: EmailService,
    private readonly firebaseNotificationService: NotificationService,
    private readonly deviceTokenService: DeviceTokenService,
    private readonly userRepository: UserRepository,
    private readonly queueService: QueueService,
  ) {
    // Initialize in-app notification subjects for different event types
    ['quiz', 'post', 'event', 'opportunity', 'announcement', 'general'].forEach(
      (eventType) => {
        this.inAppNotificationSubjects.set(eventType, new Subject<any>());
      },
    );
  }

  /**
   * Utility function to validate that a user exists, is active, and not deleted
   * Uses the centralized UserRepository validation method
   * @param userId The user ID to validate
   * @param context Context for logging (e.g., 'email notification')
   * @returns The user object with email if valid, null if not valid
   */
  private async validateActiveUser(
    userId: string,
    context: string,
  ): Promise<{ email?: string; state: string } | null> {
    try {
      // Use centralized validation from UserRepository
      const validUser = await this.userRepository.validateActiveUser(userId);
      if (!validUser) {
        this.logger.warn(
          `User validation failed for ${context} (user ${userId})`,
        );
        return null;
      }

      // Get user email for notification purposes
      const [user] = await this.drizzle.db
        .select({ email: users.email, state: users.state })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        this.logger.warn(`User with ID ${userId} not found for ${context}`);
        return null;
      }

      return user;
    } catch (error: any) {
      this.logger.error(
        `Error validating user ${userId} for ${context}:`,
        error?.stack,
      );
      return null;
    }
  }

  /**
   * Send email notification
   */
  async sendEmailNotification(
    userId: string,
    subject: string,
    body: string,
    data: Record<string, any>,
  ): Promise<void> {
    try {
      // Validate user is active
      const user = await this.validateActiveUser(userId, 'email notification');
      if (!user) {
        throw new NotFoundException(
          `User with ID ${userId} not found or not active`,
        );
      }

      // Determine which template to use based on the notification type
      let template = 'notification';

      // Use specific templates based on notification type
      if (data.type === 'raffle_winner') {
        template = 'raffle-winner';
      } else if (data.type === 'opportunity_selection') {
        template = 'opportunity-selection';
      } else if (
        data.module === 'quiz' ||
        data.type === 'new_quiz' ||
        data.type === 'active_quiz'
      ) {
        template = 'quiz';
        // Extract quiz title from the subject if available
        if (subject && subject.includes('Quiz:')) {
          data.quizTitle = subject.split('Quiz:')[1]?.trim() ?? '';
        }
      } else if (
        data.module === 'post' ||
        data.type === 'new_post' ||
        data.type === 'new_event' ||
        data.type === 'new_opportunity'
      ) {
        // Use the appropriate template based on the post type
        if (data.type === 'new_event') {
          template = 'post';
        } else if (data.type === 'new_opportunity') {
          template = 'post';
        } else {
          template = 'post';
        }

        // For post notifications, we already have postTitle in the data
        // We don't need to extract it from the subject
        if (!data.postTitle && !data.post_title) {
          // Only if postTitle is not already set, try to extract it from subject
          if (subject) {
            // Just use the subject as is - it should be "New Post", "New Event", etc.
            data.postTitle = subject;
          }
        }
      } else if (data.module === 'event' || data.type === 'event_reminder') {
        template = 'event';
      }

      // Use userName from data or default
      const userName = data.userName || 'Reach User';

      // Prepare context with all necessary data
      const emailContext = {
        subject,
        title: subject,
        message: body,
        greeting: `Hello ${userName},`,
        userName: userName,
        // Ensure these fields are always present for post notifications
        // Don't include post title in the message, only in the header
        postType: data.postType || 'post',
        postId: data.postId || '',
        // Store the post title for the header but not for the message
        post_title: data.postTitle || data.post_title || subject,
        postTitle: data.postTitle || data.post_title || subject,
        postDescription: data.postDescription || body,
        // Include all other data
        ...data,
      };

      // Send email with job ID to prevent duplicates
      await this.emailService.sendCustomEmail({
        email: user.email!,
        subject,
        template,
        context: emailContext,
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to send email notification to user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Send push notification via queue for better dashboard visibility
   */
  async sendPushNotification(
    userId: string,
    title: string,
    body: string,
    data: Record<string, any>,
  ): Promise<void> {
    try {
      // Validate user is active
      const user = await this.validateActiveUser(userId, 'push notification');
      if (!user) {
        return;
      }

      // Get user device tokens
      const deviceTokens =
        await this.deviceTokenService.getUserDeviceTokens(userId);

      if (deviceTokens.length === 0) {
        this.logger.warn(`No device tokens found for user ${userId}`);
        return;
      }

      // Add push notification jobs to queue for each device token
      // This ensures jobs appear in the BullMQ dashboard
      for (const token of deviceTokens) {
        const jobId = `push-${userId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        await this.queueService.addSinglePushJob(
          {
            token,
            title,
            body,
            data: {
              ...data,
              userId,
              timestamp: new Date().toISOString(),
            },
          },
          jobId,
        );

        // Only log push notification jobs in development or when explicitly enabled
        if (
          process.env.NODE_ENV === 'development' ||
          process.env.DEBUG_PUSH_NOTIFICATIONS === 'true'
        ) {
          this.logger.debug(
            `Added push notification job ${jobId} for user ${userId}`,
          );
        }
      }
    } catch (error: any) {
      this.logger.error(
        `Failed to queue push notification for user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Send in-app notification
   */
  async sendInAppNotification(
    userId: string,
    title: string,
    body: string,
    data: Record<string, any>,
    eventType: string,
  ): Promise<void> {
    try {
      // Validate user is active
      const user = await this.validateActiveUser(userId, 'in-app notification');
      if (!user) {
        return;
      }

      // First, use the traditional Subject approach for backward compatibility
      const subject = this.inAppNotificationSubjects.get(eventType);
      if (subject) {
        // Send notification to the subject
        subject.next({
          userId,
          title,
          body,
          data,
          timestamp: new Date(),
        });
      }

      // Only log in-app notifications in development or when explicitly enabled
      if (
        process.env.NODE_ENV === 'development' ||
        process.env.NODE_ENV === 'staging' ||
        process.env.NODE_ENV === 'local' ||
        process.env.DEBUG_IN_APP_NOTIFICATIONS === 'true'
      ) {
        this.logger.debug(
          `In-app notification sent via subject for user ${userId} (module: ${eventType})`,
        );
      }
    } catch (error: any) {
      this.logger.error(
        `Failed to send in-app notification to user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Get in-app notification stream for a specific event type
   */
  getInAppNotificationStream(eventType: string) {
    return this.inAppNotificationSubjects.get(eventType);
  }

  /**
   * Get in-app notification subject for a specific module
   * @param module The module name
   * @returns The Subject for the module, or undefined if not found
   */
  getInAppNotificationSubject(module: string): Subject<any> | undefined {
    return this.inAppNotificationSubjects.get(module);
  }

  /**
   * Send custom email
   */
  async sendCustomEmail(params: {
    email: string;
    subject: string;
    template: string;
    context: Record<string, any>;
  }): Promise<void> {
    const { email, subject, template, context } = params;

    try {
      await this.emailService.sendCustomEmail({
        email,
        subject,
        template,
        context,
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to send custom email to ${email}:`,
        error?.stack,
      );
      this.logger.error(
        `Email details - Template: ${template}, Subject: ${subject}`,
      );
      throw error;
    }
  }
}
