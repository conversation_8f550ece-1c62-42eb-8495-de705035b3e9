{"version": "7", "dialect": "postgresql", "entries": [{"idx": 0, "version": "7", "when": 1739135421499, "tag": "0000_initial-db-schema-after-introspect", "breakpoints": true}, {"idx": 1, "version": "7", "when": 1739872438478, "tag": "0001_added_deleted_to_users_student_profile", "breakpoints": true}, {"idx": 2, "version": "7", "when": 1740058390755, "tag": "0002_add-about-field-on-student-profile", "breakpoints": true}, {"idx": 3, "version": "7", "when": 1740489868374, "tag": "0003_add-ondelete-cascade-on-student-club-memberships-and-is-global-on-posts", "breakpoints": true}, {"idx": 4, "version": "7", "when": 1745506346447, "tag": "0004_bitter_stardust", "breakpoints": true}, {"idx": 5, "version": "7", "when": 1745507076780, "tag": "0005_friendly_stellaris", "breakpoints": true}, {"idx": 6, "version": "7", "when": 1745834525345, "tag": "0006_add_notify_users_to_posts", "breakpoints": true}, {"idx": 7, "version": "7", "when": 1746432201736, "tag": "0007_add-domain-to-institution", "breakpoints": true}, {"idx": 8, "version": "7", "when": 1747149784844, "tag": "0008_add-username-to-profile", "breakpoints": true}, {"idx": 9, "version": "7", "when": 1747374437673, "tag": "0009_add-post-images", "breakpoints": true}, {"idx": 10, "version": "7", "when": 1748399564038, "tag": "0010_military_blockbuster", "breakpoints": true}, {"idx": 11, "version": "7", "when": 1748399987071, "tag": "0011_massive_swarm", "breakpoints": true}, {"idx": 12, "version": "7", "when": 1748859233919, "tag": "0012_add-skill-and-skill-categories", "breakpoints": true}, {"idx": 13, "version": "7", "when": 1749476008418, "tag": "0013_add-scheduled-field-for-posts", "breakpoints": true}, {"idx": 14, "version": "7", "when": 1749476008419, "tag": "0014_fix-user-deletion-foreign-key-constraints", "breakpoints": true}, {"idx": 15, "version": "7", "when": 1750339762132, "tag": "0015_demonic_santa_claus", "breakpoints": true}]}